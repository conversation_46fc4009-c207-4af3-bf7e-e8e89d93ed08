import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:tolk/models/local_models.dart';
import 'package:tolk/services/local_database_service.dart';
import 'package:tolk/services/offline_chat_service.dart';
import 'package:tolk/models/chat_models.dart';

void main() {
  group('Offline Chat Tests', () {
    late LocalDatabaseService localDb;
    late OfflineChatService offlineChatService;

    setUpAll(() async {
      // Initialize Hive for testing
      Hive.init('./test/hive_test_db');

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(LocalMessageAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(LocalChatRoomAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(LocalUserAdapter());
      }
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(SyncStatusAdapter());
      }
    });

    setUp(() async {
      localDb = LocalDatabaseService();
      await localDb.initialize();

      offlineChatService = OfflineChatService();
      await offlineChatService.initialize();
    });

    tearDown(() async {
      // Clear test data
      await localDb.clearAllData();
    });

    test('should save message locally when offline', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_1';
      const senderId = 'test_user_1';
      const messageText = 'Hello, this is a test message!';

      // Act
      final messageId = await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: messageText,
        type: MessageType.text,
      );

      // Assert
      expect(messageId, isNotNull);
      expect(messageId, isNotEmpty);

      // Verify message is saved locally
      final localMessages = localDb.getMessagesForChatRoom(chatRoomId);
      expect(localMessages.length, equals(1));
      expect(localMessages.first.text, equals(messageText));
      expect(localMessages.first.senderId, equals(senderId));
      expect(localMessages.first.isSynced, isFalse);
    });

    test('should retrieve messages from local storage', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_2';
      const senderId = 'test_user_1';

      // Save multiple messages
      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 1',
        type: MessageType.text,
      );

      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 2',
        type: MessageType.text,
      );

      // Act
      final messagesStream = offlineChatService.getMessages(chatRoomId);
      final messages = await messagesStream.first;

      // Assert
      expect(messages.length, equals(2));
      expect(messages[0].text, equals('Message 1'));
      expect(messages[1].text, equals('Message 2'));
    });

    test('should create chat room locally when offline', () async {
      // Arrange
      const participants = ['user1', 'user2'];
      const isGroupChat = false;

      // Act
      final chatRoomId = await offlineChatService.createOrGetChatRoom(
        participants: participants,
        isGroupChat: isGroupChat,
      );

      // Assert
      expect(chatRoomId, isNotNull);
      expect(chatRoomId, isNotEmpty);

      // Verify chat room is saved locally
      final chatRoom = localDb.getChatRoom(chatRoomId);
      expect(chatRoom, isNotNull);
      expect(chatRoom!.participants, equals(participants));
      expect(chatRoom.isGroupChat, equals(isGroupChat));
      expect(chatRoom.isSynced, isFalse);
    });

    test('should mark messages as read locally', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_3';
      const currentUserId = 'current_user';

      // Send a message
      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Test message',
        type: MessageType.text,
      );

      // Act
      await offlineChatService.markMessagesAsRead(chatRoomId);

      // Assert
      final localMessages = localDb.getMessagesForChatRoom(chatRoomId);
      expect(localMessages.length, equals(1));
      expect(localMessages.first.readBy, contains(currentUserId));
    });

    test('should track unsynced message count', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_4';

      // Act - Send multiple messages
      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 1',
        type: MessageType.text,
      );

      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 2',
        type: MessageType.text,
      );

      // Assert
      final unsyncedCount = offlineChatService.getUnsyncedMessageCount();
      expect(unsyncedCount, equals(2));
    });

    test('should handle media messages offline', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_5';
      const mediaUrl = 'file:///path/to/image.jpg';

      // Act
      final messageId = await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        mediaUrl: mediaUrl,
        type: MessageType.image,
      );

      // Assert
      expect(messageId, isNotNull);

      final localMessages = localDb.getMessagesForChatRoom(chatRoomId);
      expect(localMessages.length, equals(1));
      expect(localMessages.first.mediaUrl, equals(mediaUrl));
      expect(localMessages.first.type, equals(MessageType.image));
    });

    test('should handle encrypted messages offline', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_6';
      const originalText = 'Secret message';
      const encryptedText = 'encrypted_secret_message';

      // Act
      final messageId = await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: originalText,
        type: MessageType.text,
        isEncrypted: true,
        encryptedText: encryptedText,
      );

      // Assert
      expect(messageId, isNotNull);

      final localMessages = localDb.getMessagesForChatRoom(chatRoomId);
      expect(localMessages.length, equals(1));
      expect(localMessages.first.text, equals(originalText));
      expect(localMessages.first.encryptedText, equals(encryptedText));
      expect(localMessages.first.isEncrypted, isTrue);
    });

    test('should provide sync statistics', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_7';

      // Send some messages
      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 1',
        type: MessageType.text,
      );

      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 2',
        type: MessageType.text,
      );

      // Act
      final stats = offlineChatService.getSyncStats();

      // Assert
      expect(stats, isA<Map<String, dynamic>>());
      expect(stats.containsKey('unsyncedMessages'), isTrue);
      expect(stats.containsKey('totalMessages'), isTrue);
      expect(stats.containsKey('totalChatRooms'), isTrue);
      expect(stats['unsyncedMessages'], equals(2));
    });

    test('should clear local data on logout', () async {
      // Arrange
      const chatRoomId = 'test_chat_room_8';

      // Send a message and create chat room
      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Test message',
        type: MessageType.text,
      );

      // Verify data exists
      expect(localDb.getMessagesForChatRoom(chatRoomId).length, equals(1));

      // Act
      await offlineChatService.clearLocalData();

      // Assert
      expect(localDb.getMessagesForChatRoom(chatRoomId).length, equals(0));
      expect(localDb.getAllChatRooms().length, equals(0));
    });
  });
}
