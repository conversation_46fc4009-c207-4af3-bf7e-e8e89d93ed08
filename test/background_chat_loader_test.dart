import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:tolk/providers/background_chat_loader.dart';
import 'package:tolk/services/offline_chat_service.dart';
import 'package:tolk/services/auth_service.dart';
import 'package:tolk/models/chat_models.dart';

// Generate mocks
@GenerateMocks([OfflineChatService, AuthService])
import 'background_chat_loader_test.mocks.dart';

void main() {
  group('BackgroundChatLoader Tests', () {
    late BackgroundChatLoader backgroundLoader;
    late MockOfflineChatService mockOfflineChatService;
    late MockAuthService mockAuthService;

    setUp(() {
      mockOfflineChatService = MockOfflineChatService();
      mockAuthService = MockAuthService();
      
      // Reset singleton instance for testing
      backgroundLoader = BackgroundChatLoader();
    });

    test('should initialize successfully', () async {
      // Arrange
      when(mockAuthService.isLoggedIn).thenReturn(true);
      when(mockOfflineChatService.initialize()).thenAnswer((_) async {});
      when(mockOfflineChatService.getChatRooms()).thenAnswer(
        (_) => Stream.value([]),
      );

      // Act
      await backgroundLoader.initialize();

      // Assert
      expect(backgroundLoader.isInitialized, isTrue);
    });

    test('should cache chat rooms when loaded', () async {
      // Arrange
      final testChatRooms = [
        ChatRoom(
          id: 'chat1',
          participants: ['user1', 'user2'],
          lastMessage: 'Hello',
          lastMessageTime: DateTime.now(),
          isGroupChat: false,
          unreadCount: {},
        ),
        ChatRoom(
          id: 'chat2',
          participants: ['user1', 'user3'],
          lastMessage: 'Hi there',
          lastMessageTime: DateTime.now(),
          isGroupChat: false,
          unreadCount: {},
        ),
      ];

      when(mockAuthService.isLoggedIn).thenReturn(true);
      when(mockOfflineChatService.initialize()).thenAnswer((_) async {});
      when(mockOfflineChatService.getChatRooms()).thenAnswer(
        (_) => Stream.value(testChatRooms),
      );
      when(mockOfflineChatService.getMessages(any, limit: anyNamed('limit')))
          .thenAnswer((_) => Stream.value([]));

      // Act
      await backgroundLoader.initialize();
      
      // Wait for stream to emit
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(backgroundLoader.hasLoadedChatRooms, isTrue);
      expect(backgroundLoader.cachedChatRooms.length, equals(2));
      expect(backgroundLoader.cachedChatRooms[0].id, equals('chat1'));
      expect(backgroundLoader.cachedChatRooms[1].id, equals('chat2'));
    });

    test('should provide cached data immediately in stream', () async {
      // Arrange
      final testChatRooms = [
        ChatRoom(
          id: 'chat1',
          participants: ['user1', 'user2'],
          lastMessage: 'Hello',
          lastMessageTime: DateTime.now(),
          isGroupChat: false,
          unreadCount: {},
        ),
      ];

      // Simulate already cached data
      when(mockAuthService.isLoggedIn).thenReturn(true);
      when(mockOfflineChatService.initialize()).thenAnswer((_) async {});
      when(mockOfflineChatService.getChatRooms()).thenAnswer(
        (_) => Stream.value(testChatRooms),
      );
      when(mockOfflineChatService.getMessages(any, limit: anyNamed('limit')))
          .thenAnswer((_) => Stream.value([]));

      await backgroundLoader.initialize();
      await Future.delayed(const Duration(milliseconds: 100));

      // Act
      final stream = backgroundLoader.getChatRoomsStream();
      final firstEmission = await stream.first;

      // Assert
      expect(firstEmission.length, equals(1));
      expect(firstEmission[0].id, equals('chat1'));
    });

    test('should track sync statistics', () async {
      // Arrange
      when(mockAuthService.isLoggedIn).thenReturn(true);
      when(mockOfflineChatService.initialize()).thenAnswer((_) async {});
      when(mockOfflineChatService.getChatRooms()).thenAnswer(
        (_) => Stream.value([]),
      );

      // Act
      await backgroundLoader.initialize();
      final stats = backgroundLoader.getSyncStats();

      // Assert
      expect(stats['isInitialized'], isTrue);
      expect(stats['hasLoadedChatRooms'], isFalse); // No data loaded yet
      expect(stats['cachedChatRoomsCount'], equals(0));
      expect(stats['cachedMessagesCount'], equals(0));
    });

    test('should clear cache properly', () async {
      // Arrange
      final testChatRooms = [
        ChatRoom(
          id: 'chat1',
          participants: ['user1', 'user2'],
          lastMessage: 'Hello',
          lastMessageTime: DateTime.now(),
          isGroupChat: false,
          unreadCount: {},
        ),
      ];

      when(mockAuthService.isLoggedIn).thenReturn(true);
      when(mockOfflineChatService.initialize()).thenAnswer((_) async {});
      when(mockOfflineChatService.getChatRooms()).thenAnswer(
        (_) => Stream.value(testChatRooms),
      );
      when(mockOfflineChatService.getMessages(any, limit: anyNamed('limit')))
          .thenAnswer((_) => Stream.value([]));

      await backgroundLoader.initialize();
      await Future.delayed(const Duration(milliseconds: 100));

      // Act
      backgroundLoader.clearAllCache();

      // Assert
      expect(backgroundLoader.hasLoadedChatRooms, isFalse);
      expect(backgroundLoader.cachedChatRooms.length, equals(0));
      
      final stats = backgroundLoader.getSyncStats();
      expect(stats['cachedChatRoomsCount'], equals(0));
      expect(stats['cachedMessagesCount'], equals(0));
    });

    test('should restart loading after clear', () async {
      // Arrange
      when(mockAuthService.isLoggedIn).thenReturn(true);
      when(mockOfflineChatService.initialize()).thenAnswer((_) async {});
      when(mockOfflineChatService.getChatRooms()).thenAnswer(
        (_) => Stream.value([]),
      );

      await backgroundLoader.initialize();

      // Act
      backgroundLoader.restart();

      // Assert
      expect(backgroundLoader.hasLoadedChatRooms, isFalse);
      expect(backgroundLoader.cachedChatRooms.length, equals(0));
    });
  });
}
