import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:tolk/providers/call_provider.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/providers/contact_provider.dart';
import 'package:tolk/providers/chat_provider.dart';
import 'package:tolk/providers/background_chat_loader.dart';
import 'package:tolk/screens/splash/splash_screen.dart';
import 'package:tolk/utils/app_strings.dart';
import 'package:tolk/widgets/incoming_call_listener.dart';
import 'package:tolk/services/notification_service.dart';
import 'package:tolk/services/auth_service.dart'; // Import AuthService
import 'package:tolk/services/local_database_service.dart';
import 'package:tolk/services/sync_service.dart';

import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  final documentsDir = await getApplicationDocumentsDirectory();
  Hive.init(documentsDir.path);

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // await FirebaseAppCheck.instance.activate(
  //   androidProvider: AndroidProvider.playIntegrity,
  //   appleProvider: AppleProvider.debug,
  // );

  // Set up background message handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Initialize Local Database Service
  await LocalDatabaseService().initialize();

  // Initialize Sync Service
  await SyncService().initialize();

  // Initialize Notification Service
  // Ensure this is called after Firebase.initializeApp and before runApp
  await NotificationService().initialize();

  runApp(const MyApp());
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase for background processing
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  print('🔔 [BACKGROUND] Handling background message: ${message.messageId}');
  print('🔔 [BACKGROUND] Title: ${message.notification?.title}');
  print('🔔 [BACKGROUND] Body: ${message.notification?.body}');
  print('🔔 [BACKGROUND] Data: ${message.data}');
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // Global navigation key for handling notifications
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  Widget build(BuildContext context) {
    // Set the navigation key for notification service
    NotificationService.navigatorKey = navigatorKey;

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => ContactProvider()),
        ChangeNotifierProvider(create: (_) => CallProvider()),
        ChangeNotifierProvider(create: (_) => ChatProvider()),
        ChangeNotifierProvider(create: (_) => BackgroundChatLoader()),
        Provider(create: (_) => AuthService()), // Add AuthService
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: AppStrings.appName,
        theme: ThemeData.dark(),
        navigatorKey: navigatorKey,
        home: const IncomingCallListener(child: SplashScreen()),
      ),
    );
  }
}
