import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/services/offline_chat_service.dart';
import 'package:tolk/services/auth_service.dart';

class BackgroundChatLoader extends ChangeNotifier {
  static final BackgroundChatLoader _instance =
      BackgroundChatLoader._internal();
  factory BackgroundChatLoader() => _instance;
  BackgroundChatLoader._internal();

  final OfflineChatService _offlineChatService = OfflineChatService();
  final AuthService _authService = AuthService();

  // Cache for chat rooms
  List<ChatRoom> _cachedChatRooms = [];
  final Map<String, List<Message>> _cachedMessages = {};

  // Loading states
  bool _isInitialized = false;
  bool _isLoadingChatRooms = false;
  bool _hasLoadedChatRooms = false;

  // Streams
  StreamSubscription<List<ChatRoom>>? _chatRoomsSubscription;
  final Map<String, StreamSubscription<List<Message>>> _messageSubscriptions =
      {};

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLoadingChatRooms => _isLoadingChatRooms;
  bool get hasLoadedChatRooms => _hasLoadedChatRooms;
  List<ChatRoom> get cachedChatRooms => List.unmodifiable(_cachedChatRooms);

  List<Message> getCachedMessages(String chatRoomId) {
    return List.unmodifiable(_cachedMessages[chatRoomId] ?? []);
  }

  /// Initialize the background loader - call this from splash screen
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🚀 [BACKGROUND_LOADER] Initializing...');

      // Initialize offline chat service
      await _offlineChatService.initialize();

      _isInitialized = true;
      debugPrint('✅ [BACKGROUND_LOADER] Initialized successfully');

      // Start background loading if user is logged in
      if (_authService.isLoggedIn) {
        _startBackgroundLoading();
      }
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Initialization error: $e');
    }
  }

  /// Start background loading of chat rooms and recent messages
  void _startBackgroundLoading() {
    if (!_isInitialized || _isLoadingChatRooms) return;

    debugPrint('🔄 [BACKGROUND_LOADER] Starting background chat loading...');
    _isLoadingChatRooms = true;
    notifyListeners();

    // Start listening to chat rooms stream
    _chatRoomsSubscription = _offlineChatService.getChatRooms().listen(
      _onChatRoomsUpdated,
      onError: _onChatRoomsError,
    );
  }

  /// Handle chat rooms updates
  void _onChatRoomsUpdated(List<ChatRoom> chatRooms) {
    debugPrint(
      '📥 [BACKGROUND_LOADER] Received ${chatRooms.length} chat rooms',
    );

    _cachedChatRooms = chatRooms;
    _isLoadingChatRooms = false;
    _hasLoadedChatRooms = true;

    // Start loading recent messages for each chat room
    _loadRecentMessagesForChatRooms(chatRooms);

    notifyListeners();
    debugPrint('✅ [BACKGROUND_LOADER] Chat rooms cached successfully');
  }

  /// Handle chat rooms loading errors
  void _onChatRoomsError(dynamic error) {
    debugPrint('❌ [BACKGROUND_LOADER] Chat rooms error: $error');
    _isLoadingChatRooms = false;
    notifyListeners();
  }

  /// Load recent messages for chat rooms (last 15 messages each)
  void _loadRecentMessagesForChatRooms(List<ChatRoom> chatRooms) {
    for (final chatRoom in chatRooms) {
      _loadRecentMessages(chatRoom.id);
    }
  }

  /// Load recent messages for a specific chat room
  void _loadRecentMessages(String chatRoomId) {
    // Cancel existing subscription if any
    _messageSubscriptions[chatRoomId]?.cancel();

    // Start listening to messages for this chat room
    _messageSubscriptions[chatRoomId] = _offlineChatService
        .getMessages(chatRoomId, limit: 15) // Load only recent messages
        .listen(
          (messages) => _onMessagesUpdated(chatRoomId, messages),
          onError: (error) => _onMessagesError(chatRoomId, error),
        );
  }

  /// Handle messages updates for a chat room
  void _onMessagesUpdated(String chatRoomId, List<Message> messages) {
    _cachedMessages[chatRoomId] = messages;
    debugPrint(
      '📨 [BACKGROUND_LOADER] Cached ${messages.length} messages for chat $chatRoomId',
    );
    notifyListeners();
  }

  /// Handle messages loading errors
  void _onMessagesError(String chatRoomId, dynamic error) {
    debugPrint(
      '❌ [BACKGROUND_LOADER] Messages error for chat $chatRoomId: $error',
    );
  }

  /// Get chat rooms stream - returns cached data immediately if available
  Stream<List<ChatRoom>> getChatRoomsStream() {
    if (_hasLoadedChatRooms) {
      // Return cached data immediately, then continue with live updates
      return Stream.value(
        _cachedChatRooms,
      ).followedBy(_offlineChatService.getChatRooms().skip(1));
    } else {
      // Return live stream if cache not ready
      return _offlineChatService.getChatRooms();
    }
  }

  /// Get messages stream for a chat room - returns cached data immediately if available
  Stream<List<Message>> getMessagesStream(String chatRoomId, {int? limit}) {
    final cachedMessages = _cachedMessages[chatRoomId];

    if (cachedMessages != null && cachedMessages.isNotEmpty) {
      // Return cached data immediately, then continue with live updates
      return Stream.fromIterable([cachedMessages]).asyncExpand((
        cachedData,
      ) async* {
        yield cachedData;
        yield* _offlineChatService
            .getMessages(chatRoomId, limit: limit)
            .skip(1);
      });
    } else {
      // Return live stream if cache not ready
      return _offlineChatService.getMessages(chatRoomId, limit: limit);
    }
  }

  /// Preload messages for a specific chat room (when user is about to open it)
  Future<void> preloadChatMessages(String chatRoomId, {int limit = 50}) async {
    try {
      debugPrint(
        '🔄 [BACKGROUND_LOADER] Preloading messages for chat $chatRoomId',
      );

      // Start loading more messages in background
      _loadRecentMessages(chatRoomId);
    } catch (e) {
      debugPrint('❌ [BACKGROUND_LOADER] Error preloading messages: $e');
    }
  }

  /// Clear cache for a specific chat room
  void clearChatCache(String chatRoomId) {
    _cachedMessages.remove(chatRoomId);
    _messageSubscriptions[chatRoomId]?.cancel();
    _messageSubscriptions.remove(chatRoomId);
    notifyListeners();
  }

  /// Clear all cached data
  void clearAllCache() {
    _cachedChatRooms.clear();
    _cachedMessages.clear();

    // Cancel all subscriptions
    for (final subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    _messageSubscriptions.clear();

    _hasLoadedChatRooms = false;
    notifyListeners();

    debugPrint('🧹 [BACKGROUND_LOADER] All cache cleared');
  }

  /// Restart background loading (useful after login/logout)
  void restart() {
    debugPrint('🔄 [BACKGROUND_LOADER] Restarting...');

    // Cancel existing subscriptions
    _chatRoomsSubscription?.cancel();
    for (final subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    _messageSubscriptions.clear();

    // Clear cache
    clearAllCache();

    // Restart if user is logged in
    if (_authService.isLoggedIn && _isInitialized) {
      _startBackgroundLoading();
    }
  }

  /// Get sync statistics
  Map<String, dynamic> getSyncStats() {
    return {
      'isInitialized': _isInitialized,
      'hasLoadedChatRooms': _hasLoadedChatRooms,
      'cachedChatRoomsCount': _cachedChatRooms.length,
      'cachedMessagesCount': _cachedMessages.values.fold(
        0,
        (sum, messages) => sum + messages.length,
      ),
      'activeMessageStreams': _messageSubscriptions.length,
    };
  }

  @override
  void dispose() {
    debugPrint('🧹 [BACKGROUND_LOADER] Disposing...');

    _chatRoomsSubscription?.cancel();
    for (final subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    _messageSubscriptions.clear();

    super.dispose();
  }
}
