import 'package:hive/hive.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/user_model.dart';

part 'local_models.g.dart';

// Hive-compatible Message model for local storage
@HiveType(typeId: 0)
class LocalMessage extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String chatRoomId;

  @HiveField(2)
  String senderId;

  @HiveField(3)
  String? text;

  @HiveField(4)
  String? mediaUrl;

  @HiveField(5)
  int type; // MessageType as int

  @HiveField(6)
  int status; // MessageStatus as int

  @HiveField(7)
  DateTime timestamp;

  @HiveField(8)
  List<String> readBy;

  @HiveField(9)
  Map<String, dynamic>? metadata;

  @HiveField(10)
  bool isEncrypted;

  @HiveField(11)
  String? encryptedText;

  @HiveField(12)
  String? encryptedMediaUrl;

  @HiveField(13)
  bool isSynced; // Track if message is synced to Firestore

  @HiveField(14)
  String? localPath; // Local file path for media

  @HiveField(15)
  bool isUploading; // Track upload status

  LocalMessage({
    required this.id,
    required this.chatRoomId,
    required this.senderId,
    this.text,
    this.mediaUrl,
    required this.type,
    required this.status,
    required this.timestamp,
    required this.readBy,
    this.metadata,
    this.isEncrypted = false,
    this.encryptedText,
    this.encryptedMediaUrl,
    this.isSynced = false,
    this.localPath,
    this.isUploading = false,
  });

  // Convert from Message to LocalMessage
  factory LocalMessage.fromMessage(Message message, {bool isSynced = true}) {
    return LocalMessage(
      id: message.id,
      chatRoomId: message.chatRoomId,
      senderId: message.senderId,
      text: message.text,
      mediaUrl: message.mediaUrl,
      type: message.type.index,
      status: message.status.index,
      timestamp: message.timestamp,
      readBy: List<String>.from(message.readBy),
      metadata:
          message.metadata != null
              ? Map<String, dynamic>.from(message.metadata!)
              : null,
      isEncrypted: message.isEncrypted,
      encryptedText: message.encryptedText,
      encryptedMediaUrl: message.encryptedMediaUrl,
      isSynced: isSynced,
      localPath: message.metadata?['localPath'],
      isUploading: message.metadata?['isUploading'] ?? false,
    );
  }

  // Convert to Message
  Message toMessage() {
    return Message(
      id: id,
      chatRoomId: chatRoomId,
      senderId: senderId,
      text: text,
      mediaUrl: mediaUrl,
      type: MessageType.values[type],
      status: MessageStatus.values[status],
      timestamp: timestamp,
      readBy: List<String>.from(readBy),
      metadata: metadata != null ? Map<String, dynamic>.from(metadata!) : null,
      isEncrypted: isEncrypted,
      encryptedText: encryptedText,
      encryptedMediaUrl: encryptedMediaUrl,
    );
  }
}

// Hive-compatible ChatRoom model for local storage
@HiveType(typeId: 1)
class LocalChatRoom extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  List<String> participants;

  @HiveField(2)
  bool isGroupChat;

  @HiveField(3)
  String? groupName;

  @HiveField(4)
  String? groupImage;

  @HiveField(5)
  String? lastMessage;

  @HiveField(6)
  DateTime? lastMessageTime;

  @HiveField(7)
  Map<String, int> unreadCount;

  @HiveField(8)
  DateTime createdAt;

  @HiveField(9)
  String createdBy;

  @HiveField(10)
  bool isEncrypted;

  @HiveField(11)
  DateTime? encryptionEnabledAt;

  @HiveField(12)
  String? encryptionEnabledBy;

  @HiveField(13)
  DateTime? encryptionDisabledAt;

  @HiveField(14)
  String? encryptionDisabledBy;

  @HiveField(15)
  bool isSynced;

  LocalChatRoom({
    required this.id,
    required this.participants,
    this.isGroupChat = false,
    this.groupName,
    this.groupImage,
    this.lastMessage,
    this.lastMessageTime,
    required this.unreadCount,
    required this.createdAt,
    required this.createdBy,
    this.isEncrypted = false,
    this.encryptionEnabledAt,
    this.encryptionEnabledBy,
    this.encryptionDisabledAt,
    this.encryptionDisabledBy,
    this.isSynced = false,
  });

  // Convert from ChatRoom to LocalChatRoom
  factory LocalChatRoom.fromChatRoom(
    ChatRoom chatRoom, {
    bool isSynced = true,
  }) {
    return LocalChatRoom(
      id: chatRoom.id,
      participants: List<String>.from(chatRoom.participants),
      isGroupChat: chatRoom.isGroupChat,
      groupName: chatRoom.groupName,
      groupImage: chatRoom.groupImage,
      lastMessage: chatRoom.lastMessage,
      lastMessageTime: chatRoom.lastMessageTime,
      unreadCount: Map<String, int>.from(chatRoom.unreadCount),
      createdAt: chatRoom.createdAt,
      createdBy: chatRoom.createdBy,
      isEncrypted: chatRoom.isEncrypted,
      encryptionEnabledAt: chatRoom.encryptionEnabledAt,
      encryptionEnabledBy: chatRoom.encryptionEnabledBy,
      encryptionDisabledAt: chatRoom.encryptionDisabledAt,
      encryptionDisabledBy: chatRoom.encryptionDisabledBy,
      isSynced: isSynced,
    );
  }

  // Convert to ChatRoom
  ChatRoom toChatRoom() {
    return ChatRoom(
      id: id,
      participants: List<String>.from(participants),
      isGroupChat: isGroupChat,
      groupName: groupName,
      groupImage: groupImage,
      lastMessage: lastMessage,
      lastMessageTime: lastMessageTime,
      unreadCount: Map<String, int>.from(unreadCount),
      createdAt: createdAt,
      createdBy: createdBy,
      isEncrypted: isEncrypted,
      encryptionEnabledAt: encryptionEnabledAt,
      encryptionEnabledBy: encryptionEnabledBy,
      encryptionDisabledAt: encryptionDisabledAt,
      encryptionDisabledBy: encryptionDisabledBy,
    );
  }
}

// Hive-compatible User model for local storage
@HiveType(typeId: 2)
class LocalUser extends HiveObject {
  @HiveField(0)
  String uid;

  @HiveField(1)
  String phoneNumber;

  @HiveField(2)
  String? name;

  @HiveField(3)
  String? profilePicture;

  @HiveField(4)
  String? status;

  @HiveField(5)
  bool isOnline;

  @HiveField(6)
  DateTime? lastSeen;

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  String translationLanguage;

  @HiveField(9)
  bool contactsImported;

  @HiveField(10)
  bool isSynced;

  LocalUser({
    required this.uid,
    required this.phoneNumber,
    this.name,
    this.profilePicture,
    this.status,
    this.isOnline = false,
    this.lastSeen,
    required this.createdAt,
    this.translationLanguage = 'en',
    this.contactsImported = false,
    this.isSynced = false,
  });

  // Convert from UserModel to LocalUser
  factory LocalUser.fromUserModel(UserModel user, {bool isSynced = true}) {
    return LocalUser(
      uid: user.uid,
      phoneNumber: user.phoneNumber,
      name: user.name,
      profilePicture: user.profilePicture,
      status: user.status,
      isOnline: user.isOnline,
      lastSeen: user.lastSeen,
      createdAt: user.createdAt,
      translationLanguage: user.translationLanguage,
      contactsImported: user.contactsImported,
      isSynced: isSynced,
    );
  }

  // Convert to UserModel
  UserModel toUserModel() {
    return UserModel(
      uid: uid,
      phoneNumber: phoneNumber,
      name: name,
      profilePicture: profilePicture,
      status: status,
      isOnline: isOnline,
      lastSeen: lastSeen,
      createdAt: createdAt,
      translationLanguage: translationLanguage,
      contactsImported: contactsImported,
    );
  }
}

// Sync status tracking
@HiveType(typeId: 3)
class SyncStatus extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String type; // 'message', 'chatroom', 'user'

  @HiveField(2)
  String entityId;

  @HiveField(3)
  String action; // 'create', 'update', 'delete'

  @HiveField(4)
  DateTime timestamp;

  @HiveField(5)
  bool isCompleted;

  @HiveField(6)
  String? errorMessage;

  @HiveField(7)
  int retryCount;

  SyncStatus({
    required this.id,
    required this.type,
    required this.entityId,
    required this.action,
    required this.timestamp,
    this.isCompleted = false,
    this.errorMessage,
    this.retryCount = 0,
  });
}
