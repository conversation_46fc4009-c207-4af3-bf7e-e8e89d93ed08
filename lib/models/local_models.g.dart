// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_models.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalMessageAdapter extends TypeAdapter<LocalMessage> {
  @override
  final int typeId = 0;

  @override
  LocalMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalMessage(
      id: fields[0] as String,
      chatRoomId: fields[1] as String,
      senderId: fields[2] as String,
      text: fields[3] as String?,
      mediaUrl: fields[4] as String?,
      type: fields[5] as int,
      status: fields[6] as int,
      timestamp: fields[7] as DateTime,
      readBy: (fields[8] as List).cast<String>(),
      metadata: (fields[9] as Map?)?.cast<String, dynamic>(),
      isEncrypted: fields[10] as bool,
      encryptedText: fields[11] as String?,
      encryptedMediaUrl: fields[12] as String?,
      isSynced: fields[13] as bool,
      localPath: fields[14] as String?,
      isUploading: fields[15] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, LocalMessage obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.chatRoomId)
      ..writeByte(2)
      ..write(obj.senderId)
      ..writeByte(3)
      ..write(obj.text)
      ..writeByte(4)
      ..write(obj.mediaUrl)
      ..writeByte(5)
      ..write(obj.type)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.timestamp)
      ..writeByte(8)
      ..write(obj.readBy)
      ..writeByte(9)
      ..write(obj.metadata)
      ..writeByte(10)
      ..write(obj.isEncrypted)
      ..writeByte(11)
      ..write(obj.encryptedText)
      ..writeByte(12)
      ..write(obj.encryptedMediaUrl)
      ..writeByte(13)
      ..write(obj.isSynced)
      ..writeByte(14)
      ..write(obj.localPath)
      ..writeByte(15)
      ..write(obj.isUploading);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LocalChatRoomAdapter extends TypeAdapter<LocalChatRoom> {
  @override
  final int typeId = 1;

  @override
  LocalChatRoom read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalChatRoom(
      id: fields[0] as String,
      participants: (fields[1] as List).cast<String>(),
      isGroupChat: fields[2] as bool,
      groupName: fields[3] as String?,
      groupImage: fields[4] as String?,
      lastMessage: fields[5] as String?,
      lastMessageTime: fields[6] as DateTime?,
      unreadCount: (fields[7] as Map).cast<String, int>(),
      createdAt: fields[8] as DateTime,
      createdBy: fields[9] as String,
      isEncrypted: fields[10] as bool,
      encryptionEnabledAt: fields[11] as DateTime?,
      encryptionEnabledBy: fields[12] as String?,
      encryptionDisabledAt: fields[13] as DateTime?,
      encryptionDisabledBy: fields[14] as String?,
      isSynced: fields[15] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, LocalChatRoom obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.participants)
      ..writeByte(2)
      ..write(obj.isGroupChat)
      ..writeByte(3)
      ..write(obj.groupName)
      ..writeByte(4)
      ..write(obj.groupImage)
      ..writeByte(5)
      ..write(obj.lastMessage)
      ..writeByte(6)
      ..write(obj.lastMessageTime)
      ..writeByte(7)
      ..write(obj.unreadCount)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.createdBy)
      ..writeByte(10)
      ..write(obj.isEncrypted)
      ..writeByte(11)
      ..write(obj.encryptionEnabledAt)
      ..writeByte(12)
      ..write(obj.encryptionEnabledBy)
      ..writeByte(13)
      ..write(obj.encryptionDisabledAt)
      ..writeByte(14)
      ..write(obj.encryptionDisabledBy)
      ..writeByte(15)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalChatRoomAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LocalUserAdapter extends TypeAdapter<LocalUser> {
  @override
  final int typeId = 2;

  @override
  LocalUser read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalUser(
      uid: fields[0] as String,
      phoneNumber: fields[1] as String,
      name: fields[2] as String?,
      profilePicture: fields[3] as String?,
      status: fields[4] as String?,
      isOnline: fields[5] as bool,
      lastSeen: fields[6] as DateTime?,
      createdAt: fields[7] as DateTime,
      translationLanguage: fields[8] as String,
      contactsImported: fields[9] as bool,
      isSynced: fields[10] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, LocalUser obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.uid)
      ..writeByte(1)
      ..write(obj.phoneNumber)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.profilePicture)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.isOnline)
      ..writeByte(6)
      ..write(obj.lastSeen)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.translationLanguage)
      ..writeByte(9)
      ..write(obj.contactsImported)
      ..writeByte(10)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalUserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SyncStatusAdapter extends TypeAdapter<SyncStatus> {
  @override
  final int typeId = 3;

  @override
  SyncStatus read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SyncStatus(
      id: fields[0] as String,
      type: fields[1] as String,
      entityId: fields[2] as String,
      action: fields[3] as String,
      timestamp: fields[4] as DateTime,
      isCompleted: fields[5] as bool,
      errorMessage: fields[6] as String?,
      retryCount: fields[7] as int,
    );
  }

  @override
  void write(BinaryWriter writer, SyncStatus obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.entityId)
      ..writeByte(3)
      ..write(obj.action)
      ..writeByte(4)
      ..write(obj.timestamp)
      ..writeByte(5)
      ..write(obj.isCompleted)
      ..writeByte(6)
      ..write(obj.errorMessage)
      ..writeByte(7)
      ..write(obj.retryCount);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
