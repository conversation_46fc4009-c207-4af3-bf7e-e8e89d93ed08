import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/local_models.dart';
import 'package:tolk/services/local_database_service.dart';
import 'package:tolk/services/sync_service.dart';
import 'package:tolk/services/chat_service.dart';

class OfflineChatService {
  static final OfflineChatService _instance = OfflineChatService._internal();
  factory OfflineChatService() => _instance;
  OfflineChatService._internal();

  final LocalDatabaseService _localDb = LocalDatabaseService();
  final SyncService _syncService = SyncService();
  final ChatService _chatService = ChatService();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Stream controllers for real-time updates
  final Map<String, StreamController<List<Message>>> _messageStreamControllers =
      {};
  final StreamController<List<ChatRoom>> _chatRoomStreamController =
      StreamController<List<ChatRoom>>.broadcast();

  String get currentUserId => _auth.currentUser?.uid ?? '';

  // Initialize the service
  Future<void> initialize() async {
    await _localDb.initialize();
    await _syncService.initialize();

    // Listen to sync status changes to update UI
    _syncService.syncStatusStream.listen((isSyncing) {
      // Refresh chat rooms when sync completes
      if (!isSyncing) {
        _refreshChatRooms();
      }
    });

    debugPrint('📱 [OFFLINE_CHAT] Offline chat service initialized');
  }

  // === MESSAGE OPERATIONS ===

  // Send message (offline-first)
  Future<String> sendMessage({
    required String chatRoomId,
    String? text,
    String? mediaUrl,
    required MessageType type,
    Map<String, dynamic>? metadata,
    bool isEncrypted = false,
    String? encryptedText,
    String? encryptedMediaUrl,
  }) async {
    try {
      // Save message locally first
      final messageId = await _syncService.saveMessageLocally(
        chatRoomId: chatRoomId,
        senderId: currentUserId,
        text: text,
        mediaUrl: mediaUrl,
        type: type,
        metadata: metadata,
        isEncrypted: isEncrypted,
        encryptedText: encryptedText,
        encryptedMediaUrl: encryptedMediaUrl,
      );

      // Immediately update the message stream
      _updateMessageStream(chatRoomId);

      // Update chat room stream
      _refreshChatRooms();

      debugPrint('📱 [OFFLINE_CHAT] Message sent offline-first: $messageId');
      return messageId;
    } catch (e) {
      debugPrint('❌ [OFFLINE_CHAT] Error sending message: $e');
      rethrow;
    }
  }

  // Get messages stream (from local storage with real-time updates)
  Stream<List<Message>> getMessages(String chatRoomId, {int? limit}) {
    if (!_messageStreamControllers.containsKey(chatRoomId)) {
      _messageStreamControllers[chatRoomId] =
          StreamController<List<Message>>.broadcast();

      // Initial load from local storage
      _updateMessageStream(chatRoomId, limit: limit);

      // Set up periodic refresh to catch synced messages
      Timer.periodic(const Duration(seconds: 5), (timer) {
        if (_messageStreamControllers.containsKey(chatRoomId)) {
          _updateMessageStream(chatRoomId, limit: limit);
        } else {
          timer.cancel();
        }
      });
    }

    return _messageStreamControllers[chatRoomId]!.stream;
  }

  // Update message stream with local data
  void _updateMessageStream(String chatRoomId, {int? limit}) {
    try {
      final localMessages = _localDb.getMessagesForChatRoom(
        chatRoomId,
        limit: limit,
      );
      final messages =
          localMessages
              .map((localMessage) => localMessage.toMessage())
              .toList();

      if (_messageStreamControllers.containsKey(chatRoomId)) {
        _messageStreamControllers[chatRoomId]!.add(messages);
      }
    } catch (e) {
      debugPrint('❌ [OFFLINE_CHAT] Error updating message stream: $e');
    }
  }

  // === CHAT ROOM OPERATIONS ===

  // Get chat rooms stream (from local storage)
  Stream<List<ChatRoom>> getChatRooms() {
    // Initial load
    _refreshChatRooms();

    // Set up periodic refresh
    Timer.periodic(const Duration(seconds: 10), (timer) {
      _refreshChatRooms();
    });

    return _chatRoomStreamController.stream;
  }

  // Refresh chat rooms from local storage
  void _refreshChatRooms() {
    try {
      final localChatRooms = _localDb.getAllChatRooms();
      final chatRooms =
          localChatRooms
              .map((localChatRoom) => localChatRoom.toChatRoom())
              .toList();
      _chatRoomStreamController.add(chatRooms);
    } catch (e) {
      debugPrint('❌ [OFFLINE_CHAT] Error refreshing chat rooms: $e');
    }
  }

  // Create or get chat room (offline-first)
  Future<String> createOrGetChatRoom({
    required List<String> participants,
    bool isGroupChat = false,
    String? groupName,
    String? groupImage,
  }) async {
    try {
      // Check if chat room exists locally first
      final localChatRooms = _localDb.getAllChatRooms();

      for (final localChatRoom in localChatRooms) {
        if (!localChatRoom.isGroupChat && !isGroupChat) {
          // For individual chats, check if participants match
          final sortedParticipants = List<String>.from(participants)..sort();
          final sortedLocalParticipants = List<String>.from(
            localChatRoom.participants,
          )..sort();

          if (listEquals(sortedParticipants, sortedLocalParticipants)) {
            return localChatRoom.id;
          }
        }
      }

      // If online, try to create/get from Firestore
      if (_syncService.isOnline) {
        try {
          final chatRoomId = await _chatService.createOrGetChatRoom(
            participants.first,
          );

          // Save to local storage
          final localChatRoom = LocalChatRoom(
            id: chatRoomId,
            participants: participants,
            isGroupChat: isGroupChat,
            groupName: groupName,
            groupImage: groupImage,
            unreadCount: {},
            createdAt: DateTime.now(),
            createdBy: currentUserId,
            isSynced: true,
          );

          await _localDb.saveChatRoom(localChatRoom);
          _refreshChatRooms();

          return chatRoomId;
        } catch (e) {
          debugPrint('❌ [OFFLINE_CHAT] Error creating chat room online: $e');
        }
      }

      // Create locally if offline or online creation failed
      final chatRoomId = DateTime.now().millisecondsSinceEpoch.toString();
      final localChatRoom = LocalChatRoom(
        id: chatRoomId,
        participants: participants,
        isGroupChat: isGroupChat,
        groupName: groupName,
        groupImage: groupImage,
        unreadCount: {},
        createdAt: DateTime.now(),
        createdBy: currentUserId,
        isSynced: false, // Will be synced later
      );

      await _localDb.saveChatRoom(localChatRoom);
      _refreshChatRooms();

      debugPrint('📱 [OFFLINE_CHAT] Chat room created locally: $chatRoomId');
      return chatRoomId;
    } catch (e) {
      debugPrint('❌ [OFFLINE_CHAT] Error creating/getting chat room: $e');
      rethrow;
    }
  }

  // Mark messages as read (offline-first)
  Future<void> markMessagesAsRead(String chatRoomId) async {
    try {
      // Update local messages
      final localMessages = _localDb.getMessagesForChatRoom(chatRoomId);

      for (final localMessage in localMessages) {
        if (!localMessage.readBy.contains(currentUserId)) {
          localMessage.readBy.add(currentUserId);
          localMessage.isSynced = false; // Mark for sync
          await localMessage.save();
        }
      }

      // Update chat room unread count
      final chatRoom = _localDb.getChatRoom(chatRoomId);
      if (chatRoom != null) {
        chatRoom.unreadCount[currentUserId] = 0;
        chatRoom.isSynced = false; // Mark for sync
        await _localDb.updateChatRoom(chatRoom);
      }

      // Update streams
      _updateMessageStream(chatRoomId);
      _refreshChatRooms();

      // Sync if online
      if (_syncService.isOnline) {
        await _syncService.forceSync();
      }

      debugPrint(
        '📱 [OFFLINE_CHAT] Messages marked as read locally: $chatRoomId',
      );
    } catch (e) {
      debugPrint('❌ [OFFLINE_CHAT] Error marking messages as read: $e');
    }
  }

  // === UTILITY METHODS ===

  // Force sync
  Future<void> forceSync() async {
    await _syncService.forceSync();
  }

  // Get sync status
  bool get isOnline => _syncService.isOnline;
  bool get isSyncing => _syncService.isSyncing;

  // Get sync statistics
  Map<String, dynamic> getSyncStats() {
    return _syncService.getSyncStats();
  }

  // Get unsynced message count
  int getUnsyncedMessageCount() {
    return _localDb.getUnsyncedMessages().length;
  }

  // Clear local data (for logout)
  Future<void> clearLocalData() async {
    await _localDb.clearAllData();

    // Close all stream controllers
    for (final controller in _messageStreamControllers.values) {
      await controller.close();
    }
    _messageStreamControllers.clear();

    debugPrint('📱 [OFFLINE_CHAT] Local data cleared');
  }

  // Dispose
  void dispose() {
    for (final controller in _messageStreamControllers.values) {
      controller.close();
    }
    _messageStreamControllers.clear();
    _chatRoomStreamController.close();
    _syncService.dispose();
  }
}
