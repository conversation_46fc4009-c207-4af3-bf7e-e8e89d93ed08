import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:tolk/models/local_models.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/services/local_database_service.dart';
import 'package:tolk/services/chat_service.dart';

class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final LocalDatabaseService _localDb = LocalDatabaseService();
  final ChatService _chatService = ChatService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _syncTimer;
  bool _isSyncing = false;
  bool _isOnline = false;

  // Sync status callbacks
  final StreamController<bool> _syncStatusController =
      StreamController<bool>.broadcast();
  Stream<bool> get syncStatusStream => _syncStatusController.stream;

  // Initialize sync service
  Future<void> initialize() async {
    await _localDb.initialize();

    // Check initial connectivity
    final connectivity = Connectivity();
    final result = await connectivity.checkConnectivity();
    _isOnline = !result.contains(ConnectivityResult.none);

    // Listen to connectivity changes
    _connectivitySubscription = connectivity.onConnectivityChanged.listen((
      result,
    ) {
      final wasOnline = _isOnline;
      _isOnline = !result.contains(ConnectivityResult.none);

      debugPrint('🔄 [SYNC] Connectivity changed: $_isOnline');

      if (!wasOnline && _isOnline) {
        // Just came online, start sync
        _startSync();
      }
    });

    // Start periodic sync if online
    if (_isOnline) {
      _startPeriodicSync();
    }

    debugPrint('🔄 [SYNC] Sync service initialized');
  }

  // Start periodic sync
  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isOnline && !_isSyncing) {
        _startSync();
      }
    });
  }

  // Start sync process
  Future<void> _startSync() async {
    if (_isSyncing || !_isOnline) return;

    _isSyncing = true;
    _syncStatusController.add(true);

    try {
      debugPrint('🔄 [SYNC] Starting sync process...');

      // Sync in order: users, chat rooms, messages
      await _syncUsers();
      await _syncChatRooms();
      await _syncMessages();

      debugPrint('🔄 [SYNC] Sync process completed successfully');
    } catch (e) {
      debugPrint('❌ [SYNC] Error during sync: $e');
    } finally {
      _isSyncing = false;
      _syncStatusController.add(false);
    }
  }

  // Sync users
  Future<void> _syncUsers() async {
    try {
      // Get current user
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      // Fetch user data from Firestore
      final userDoc =
          await _firestore.collection('users').doc(currentUser.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final user = UserModel.fromMap(userData);
        final localUser = LocalUser.fromUserModel(user, isSynced: true);
        await _localDb.saveUser(localUser);
      }
    } catch (e) {
      debugPrint('❌ [SYNC] Error syncing users: $e');
    }
  }

  // Sync chat rooms
  Future<void> _syncChatRooms() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      // Get chat rooms from Firestore
      final chatRoomsQuery =
          await _firestore
              .collection('chatRooms')
              .where('participants', arrayContains: currentUser.uid)
              .get();

      for (final doc in chatRoomsQuery.docs) {
        final data = doc.data();
        final chatRoom = ChatRoom.fromMap(data, doc.id);
        final localChatRoom = LocalChatRoom.fromChatRoom(
          chatRoom,
          isSynced: true,
        );
        await _localDb.saveChatRoom(localChatRoom);
      }

      debugPrint('🔄 [SYNC] Synced ${chatRoomsQuery.docs.length} chat rooms');
    } catch (e) {
      debugPrint('❌ [SYNC] Error syncing chat rooms: $e');
    }
  }

  // Sync messages
  Future<void> _syncMessages() async {
    try {
      // First, upload unsynced local messages
      await _uploadUnsyncedMessages();

      // Then, download new messages from Firestore
      await _downloadNewMessages();
    } catch (e) {
      debugPrint('❌ [SYNC] Error syncing messages: $e');
    }
  }

  // Upload unsynced messages to Firestore
  Future<void> _uploadUnsyncedMessages() async {
    try {
      final unsyncedMessages = _localDb.getUnsyncedMessages();

      for (final localMessage in unsyncedMessages) {
        try {
          // Convert to Message and upload
          final message = localMessage.toMessage();

          // Create message data for Firestore
          final messageData = {
            'chatRoomId': message.chatRoomId,
            'senderId': message.senderId,
            'text': message.text,
            'mediaUrl': message.mediaUrl,
            'type': message.type.index,
            'status': message.status.index,
            'timestamp': Timestamp.fromDate(message.timestamp),
            'readBy': message.readBy,
            'metadata': message.metadata,
            'isEncrypted': message.isEncrypted,
            'encryptedText': message.encryptedText,
            'encryptedMediaUrl': message.encryptedMediaUrl,
          };

          // Upload to Firestore
          await _firestore
              .collection('messages')
              .doc(localMessage.id)
              .set(messageData);

          // Mark as synced locally
          await _localDb.updateMessageSyncStatus(localMessage.id, true);

          debugPrint('🔄 [SYNC] Uploaded message: ${localMessage.id}');
        } catch (e) {
          debugPrint('❌ [SYNC] Error uploading message ${localMessage.id}: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ [SYNC] Error uploading unsynced messages: $e');
    }
  }

  // Download new messages from Firestore
  Future<void> _downloadNewMessages() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      // Get all local chat rooms
      final localChatRooms = _localDb.getAllChatRooms();

      for (final chatRoom in localChatRooms) {
        // Get latest local message timestamp for this chat room
        final localMessages = _localDb.getMessagesForChatRoom(chatRoom.id);
        DateTime? lastMessageTime;

        if (localMessages.isNotEmpty) {
          lastMessageTime = localMessages.first.timestamp;
        }

        // Query Firestore for newer messages
        Query query = _firestore
            .collection('messages')
            .where('chatRoomId', isEqualTo: chatRoom.id)
            .orderBy('timestamp', descending: true)
            .limit(50); // Limit to prevent large downloads

        if (lastMessageTime != null) {
          query = query.where(
            'timestamp',
            isGreaterThan: Timestamp.fromDate(lastMessageTime),
          );
        }

        final messagesQuery = await query.get();

        for (final doc in messagesQuery.docs) {
          try {
            final data = doc.data() as Map<String, dynamic>;
            final message = Message.fromMap(data, doc.id);
            final localMessage = LocalMessage.fromMessage(
              message,
              isSynced: true,
            );
            await _localDb.saveMessage(localMessage);
          } catch (e) {
            debugPrint('❌ [SYNC] Error processing message ${doc.id}: $e');
          }
        }

        if (messagesQuery.docs.isNotEmpty) {
          debugPrint(
            '🔄 [SYNC] Downloaded ${messagesQuery.docs.length} new messages for chat ${chatRoom.id}',
          );
        }
      }
    } catch (e) {
      debugPrint('❌ [SYNC] Error downloading new messages: $e');
    }
  }

  // Save message locally (for offline-first approach)
  Future<String> saveMessageLocally({
    required String chatRoomId,
    required String senderId,
    String? text,
    String? mediaUrl,
    required MessageType type,
    Map<String, dynamic>? metadata,
    bool isEncrypted = false,
    String? encryptedText,
    String? encryptedMediaUrl,
  }) async {
    try {
      // Generate unique ID
      final messageId = _firestore.collection('messages').doc().id;

      // Create local message
      final localMessage = LocalMessage(
        id: messageId,
        chatRoomId: chatRoomId,
        senderId: senderId,
        text: text,
        mediaUrl: mediaUrl,
        type: type.index,
        status: MessageStatus.sending.index,
        timestamp: DateTime.now(),
        readBy: [senderId],
        metadata: metadata,
        isEncrypted: isEncrypted,
        encryptedText: encryptedText,
        encryptedMediaUrl: encryptedMediaUrl,
        isSynced: false, // Will be synced later
      );

      // Save locally
      await _localDb.saveMessage(localMessage);

      // Update chat room last message
      final chatRoom = _localDb.getChatRoom(chatRoomId);
      if (chatRoom != null) {
        chatRoom.lastMessage = text ?? 'Media';
        chatRoom.lastMessageTime = DateTime.now();
        chatRoom.isSynced = false;
        await _localDb.updateChatRoom(chatRoom);
      }

      // Trigger sync if online
      if (_isOnline) {
        _startSync();
      }

      debugPrint('💾 [SYNC] Message saved locally: $messageId');
      return messageId;
    } catch (e) {
      debugPrint('❌ [SYNC] Error saving message locally: $e');
      rethrow;
    }
  }

  // Force sync (manual trigger)
  Future<void> forceSync() async {
    if (!_isOnline) {
      debugPrint('🔄 [SYNC] Cannot force sync - offline');
      return;
    }

    await _startSync();
  }

  // Check if online
  bool get isOnline => _isOnline;

  // Check if syncing
  bool get isSyncing => _isSyncing;

  // Get sync statistics
  Map<String, dynamic> getSyncStats() {
    final unsyncedMessages = _localDb.getUnsyncedMessages();
    final dbStats = _localDb.getDatabaseStats();

    return {
      'isOnline': _isOnline,
      'isSyncing': _isSyncing,
      'unsyncedMessages': unsyncedMessages.length,
      'totalMessages': dbStats['messages'] ?? 0,
      'totalChatRooms': dbStats['chatRooms'] ?? 0,
    };
  }

  // Dispose
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    _syncStatusController.close();
  }
}
