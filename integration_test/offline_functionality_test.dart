import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:tolk/main.dart' as app;
import 'package:tolk/services/local_database_service.dart';
import 'package:tolk/services/offline_chat_service.dart';
import 'package:tolk/models/chat_models.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Offline Functionality Integration Tests', () {
    late LocalDatabaseService localDb;
    late OfflineChatService offlineChatService;

    setUpAll(() async {
      // Initialize the app
      await app.main();
      
      // Wait for initialization
      await Future.delayed(const Duration(seconds: 2));
      
      localDb = LocalDatabaseService();
      offlineChatService = OfflineChatService();
      await offlineChatService.initialize();
    });

    testWidgets('should save and retrieve messages locally', (WidgetTester tester) async {
      // Arrange
      const chatRoomId = 'integration_test_chat_1';
      const messageText = 'Integration test message';

      // Act - Send a message
      final messageId = await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: messageText,
        type: MessageType.text,
      );

      // Assert - Message should be saved
      expect(messageId, isNotNull);
      expect(messageId, isNotEmpty);

      // Retrieve messages
      final messagesStream = offlineChatService.getMessages(chatRoomId);
      final messages = await messagesStream.first;

      expect(messages.length, greaterThan(0));
      expect(messages.any((m) => m.text == messageText), isTrue);
    });

    testWidgets('should create chat room locally', (WidgetTester tester) async {
      // Arrange
      const participants = ['user1', 'user2'];

      // Act
      final chatRoomId = await offlineChatService.createOrGetChatRoom(
        participants: participants,
        isGroupChat: false,
      );

      // Assert
      expect(chatRoomId, isNotNull);
      expect(chatRoomId, isNotEmpty);

      // Verify chat room exists locally
      final chatRoom = localDb.getChatRoom(chatRoomId);
      expect(chatRoom, isNotNull);
      expect(chatRoom!.participants, equals(participants));
    });

    testWidgets('should track unsynced messages', (WidgetTester tester) async {
      // Arrange
      const chatRoomId = 'integration_test_chat_2';

      // Act - Send multiple messages
      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 1',
        type: MessageType.text,
      );

      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Message 2',
        type: MessageType.text,
      );

      // Assert
      final unsyncedCount = offlineChatService.getUnsyncedMessageCount();
      expect(unsyncedCount, greaterThan(0));

      final stats = offlineChatService.getSyncStats();
      expect(stats['unsyncedMessages'], greaterThan(0));
      expect(stats['totalMessages'], greaterThan(0));
    });

    testWidgets('should handle media messages', (WidgetTester tester) async {
      // Arrange
      const chatRoomId = 'integration_test_chat_3';
      const mediaUrl = 'file:///test/image.jpg';

      // Act
      final messageId = await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        mediaUrl: mediaUrl,
        type: MessageType.image,
      );

      // Assert
      expect(messageId, isNotNull);

      final messagesStream = offlineChatService.getMessages(chatRoomId);
      final messages = await messagesStream.first;

      final mediaMessage = messages.firstWhere((m) => m.mediaUrl == mediaUrl);
      expect(mediaMessage.type, equals(MessageType.image));
      expect(mediaMessage.mediaUrl, equals(mediaUrl));
    });

    testWidgets('should handle encrypted messages', (WidgetTester tester) async {
      // Arrange
      const chatRoomId = 'integration_test_chat_4';
      const originalText = 'Secret message';
      const encryptedText = 'encrypted_secret';

      // Act
      final messageId = await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: originalText,
        type: MessageType.text,
        isEncrypted: true,
        encryptedText: encryptedText,
      );

      // Assert
      expect(messageId, isNotNull);

      final messagesStream = offlineChatService.getMessages(chatRoomId);
      final messages = await messagesStream.first;

      final encryptedMessage = messages.firstWhere((m) => m.isEncrypted == true);
      expect(encryptedMessage.text, equals(originalText));
      expect(encryptedMessage.encryptedText, equals(encryptedText));
      expect(encryptedMessage.isEncrypted, isTrue);
    });

    testWidgets('should mark messages as read', (WidgetTester tester) async {
      // Arrange
      const chatRoomId = 'integration_test_chat_5';

      // Send a message first
      await offlineChatService.sendMessage(
        chatRoomId: chatRoomId,
        text: 'Test read message',
        type: MessageType.text,
      );

      // Act
      await offlineChatService.markMessagesAsRead(chatRoomId);

      // Assert - This test mainly verifies no errors occur
      // In a real app, you'd check the read status in the UI
      expect(true, isTrue); // Placeholder assertion
    });

    tearDownAll(() async {
      // Clean up test data
      await offlineChatService.clearLocalData();
    });
  });
}
