# Offline Functionality Testing Guide

This guide provides step-by-step instructions for manually testing the offline functionality that has been implemented in the Tolk chat application.

## Prerequisites

1. Ensure the app is built and running on a device or emulator
2. Have Firebase authentication working
3. Have at least one other user to test chat functionality

## Test Scenarios

### 1. Basic Offline Message Sending

**Objective**: Verify messages can be sent and stored locally when offline

**Steps**:
1. Open the app and log in
2. Navigate to a chat or create a new chat
3. Turn off internet connectivity (airplane mode or disable WiFi/mobile data)
4. Send several text messages
5. Verify messages appear immediately in the chat
6. Check that offline indicator shows "Offline" status
7. Turn internet back on
8. Verify messages sync to Firestore and appear on other devices

**Expected Results**:
- Messages appear instantly in chat when sent offline
- Offline indicator shows disconnected state
- Messages sync successfully when connectivity is restored
- No duplicate messages appear after sync

### 2. App Restart Persistence

**Objective**: Verify offline messages persist across app restarts

**Steps**:
1. Follow steps 1-5 from Test 1 (send messages while offline)
2. Force close the app completely
3. Restart the app
4. Navigate back to the same chat
5. Verify previously sent offline messages are still visible
6. Turn internet back on
7. Verify messages sync properly

**Expected Results**:
- All offline messages are visible after app restart
- Messages maintain correct order and timestamps
- Sync works properly after restart

### 3. Media Message Offline Handling

**Objective**: Test sending images, videos, and voice messages offline

**Steps**:
1. Go offline (airplane mode)
2. Try to send an image from gallery
3. Try to send a video from gallery
4. Try to record and send a voice message
5. Verify all media messages appear in chat with loading indicators
6. Go back online
7. Verify media uploads and syncs properly

**Expected Results**:
- Media messages appear immediately with upload pending indicators
- Files are queued for upload when offline
- Media uploads successfully when connectivity returns
- Recipients receive media messages after sync

### 4. Encrypted Message Offline Support

**Objective**: Verify encrypted messages work offline

**Steps**:
1. Enable encryption for a chat
2. Go offline
3. Send several encrypted messages
4. Verify messages appear as encrypted locally
5. Go back online
6. Verify encrypted messages sync properly
7. Check that decryption works on other devices

**Expected Results**:
- Encrypted messages are stored locally in encrypted form
- Messages decrypt properly when viewed offline
- Encrypted messages sync correctly to Firestore
- End-to-end encryption is maintained

### 5. Chat Room Creation Offline

**Objective**: Test creating new chats while offline

**Steps**:
1. Go offline
2. Try to start a new chat with a contact
3. Send messages in the new chat
4. Go back online
5. Verify chat room is created on Firestore
6. Verify messages appear for the other user

**Expected Results**:
- New chat can be created offline
- Messages are queued for the new chat
- Chat room syncs to Firestore when online
- Other user receives chat invitation and messages

### 6. Read Status Offline Handling

**Objective**: Test message read status when offline

**Steps**:
1. Receive messages while online
2. Go offline
3. Open and read the messages
4. Go back online
5. Verify read status syncs properly

**Expected Results**:
- Read status is tracked locally when offline
- Read receipts sync when connectivity returns
- Sender sees updated read status

### 7. Sync Conflict Resolution

**Objective**: Test handling of sync conflicts

**Steps**:
1. Have two devices with the same user account
2. Go offline on both devices
3. Send different messages from each device
4. Bring both devices back online
5. Verify all messages appear on both devices
6. Check for duplicate or missing messages

**Expected Results**:
- All messages from both devices appear
- No duplicate messages
- Messages maintain proper chronological order
- No data loss occurs

### 8. Large Message Volume Offline

**Objective**: Test performance with many offline messages

**Steps**:
1. Go offline
2. Send 50+ messages rapidly
3. Include mix of text, media, and encrypted messages
4. Monitor app performance and responsiveness
5. Go back online
6. Monitor sync performance

**Expected Results**:
- App remains responsive during bulk message sending
- All messages are stored locally
- Sync completes successfully without errors
- No memory issues or crashes

## UI Indicators to Verify

### Offline Indicator
- Shows "Offline" when disconnected
- Shows sync progress when reconnecting
- Shows number of unsynced messages

### Message Status Icons
- Pending upload icon for unsynced messages
- Success icon after successful sync
- Error icon if sync fails

### Loading States
- Media upload progress indicators
- Chat loading from local storage
- Sync status in chat list

## Performance Metrics to Monitor

1. **Message Send Speed**: Messages should appear instantly when sent offline
2. **App Startup Time**: Should load quickly from local storage
3. **Sync Speed**: Reasonable sync time when connectivity returns
4. **Memory Usage**: No significant memory leaks during offline operation
5. **Battery Usage**: Minimal battery drain from local storage operations

## Troubleshooting Common Issues

### Messages Not Appearing After Sync
- Check Firebase console for message data
- Verify user authentication is working
- Check app logs for sync errors

### Duplicate Messages
- Clear local storage and test again
- Check for race conditions in sync logic
- Verify message ID generation is unique

### Poor Performance
- Check local database size
- Monitor memory usage during testing
- Verify background sync is not blocking UI

### Sync Failures
- Check internet connectivity quality
- Verify Firebase permissions
- Check for API rate limiting

## Automated Testing

For automated testing, run the integration tests:

```bash
flutter test integration_test/offline_functionality_test.dart
```

This will verify core offline functionality programmatically.

## Reporting Issues

When reporting issues, please include:
1. Device type and OS version
2. Steps to reproduce
3. Expected vs actual behavior
4. App logs if available
5. Network conditions during testing

## Success Criteria

The offline functionality is working correctly if:
- ✅ Messages send instantly when offline
- ✅ Data persists across app restarts
- ✅ Sync works reliably when connectivity returns
- ✅ No data loss or duplication occurs
- ✅ UI indicators accurately reflect sync status
- ✅ Performance remains acceptable
- ✅ All message types (text, media, encrypted) work offline
